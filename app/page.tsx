"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ThemeToggle } from "@/components/ui/themeToggle";
import { authClient } from "@/lib/auth-client";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

export default function Home() {
  const router = useRouter();
  const { data: session } = authClient.useSession();
  async function signOut() {
    try {
      console.log("Starting logout...");
      await authClient.signOut({
        fetchOptions: {
          onSuccess: () => {
            console.log("Logout successful, redirecting...");
            router.push("/login");
            toast.success("Signed out successfully");
          },
          onError: (error) => {
            console.error("Logout error:", error);
          },
        },
      });
      console.log("Logout completed");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  }

  return (
    <div className="p-24">
      <h1>Hello world!</h1>
      <ThemeToggle />

      {session ? (
        <div>
          <p>{session.user.name} </p>
          <Button onClick={signOut}>Logout</Button>
        </div>
      ) : (
        <p>Please log in</p>
      )}
    </div>
  );
}
