"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { ThemeToggle } from "@/components/ui/themeToggle";
import { authClient } from "@/lib/auth-client";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface featureProps {
  title: string;
  description: string;
  icon: string;
}

const features: featureProps[] = [
  {
    title: "Comprehensive courses",
    description:
      "Access a wide range of carefully curated courses designed by industry experts.",
    icon: "📚",
  },
  {
    title: "Interactive Learning",
    description:
      "Engage with interactive content, quizzes, and assignments to enhance your learning experience.",
    icon: "🎮",
  },
  {
    title: "Progess tracking",
    description:
      "Monitor your progress and acheivements with detailed analytics and personalized dashboards.",
    icon: "📈",
  },
  {
    title: "Community Support",
    description:
      "Join a vibrant community for each course and meet and share your knowledge with each other",
    icon: "🧑‍🤝‍🧑",
  },
];

export default function Home() {
  const router = useRouter();
  const { data: session } = authClient.useSession();
  async function signOut() {
    try {
      console.log("Starting logout...");
      await authClient.signOut({
        fetchOptions: {
          onSuccess: () => {
            console.log("Logout successful, redirecting...");
            router.push("/login");
            toast.success("Signed out successfully");
          },
          onError: (error) => {
            console.error("Logout error:", error);
          },
        },
      });
      console.log("Logout completed");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  }

  return (
    <>
      <section className="relative py-20">
        <div className="flex flex-col items-center text-center space-y-8">
          <Badge variant={"outline"}>The future of online education</Badge>
          <h1 className="text-4xl md:text-6xl font-bold tracking-tight">
            Elevate your Learning Experience
          </h1>
          <p className="max-w-[700px] text-muted-foreground md:text-xl">
            Discover new way to learn with our modern, interactive learning
            managment system. Access course from anywhere.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 mt-8">
            <Link
              className={buttonVariants({
                size: "lg",
              })}
              href="/courses"
            >
              Explore Courses
            </Link>

            <Link
              className={buttonVariants({
                size: "lg",
                variant: "outline",
              })}
              href="/login"
            >
              Sign In
            </Link>
          </div>
        </div>
      </section>

      <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-col-4 gap-6">
        {features.map((features, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="text-4xl mb-4">{features.icon}</div>
              <CardTitle>{features.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">{features.description}</p>
            </CardContent>
          </Card>
        ))}
      </section>
    </>
  );
}
