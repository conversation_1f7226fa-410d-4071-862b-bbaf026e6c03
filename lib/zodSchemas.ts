import { z } from "zod";

export const courseLevels = ["Beginner", "Intermediate", "Advanced"] as const;
export const courseStatus = ["Draft", "Published", "Archived"] as const;

export const courseCategories = [
  "Development",
  "Business",
  "Finance",
  "Health",
  "IT & Software",
  "Personal Development",
  "Design",
  "Marketing",
  "Office Productivity",
  "Music",
  "Teaching and Academics",
] as const;

export const courseSchema = z.object({
  title: z
    .string()
    .min(3, { message: "Title must be at least 3 characters long" })
    .max(100, { message: "Title must be atmost 100 words" }),
  description: z
    .string()
    .min(3, { message: "Description must be at least 3 characters long" }),
  fileKey: z.string().min(1, { message: "file is required" }),
  price: z.number().min(1, { message: "Price must be a positive number" }),
  duration: z
    .number()
    .min(1, { message: "Duration must be atleast 1 hour" })
    .max(500, {
      message: "Maximum course length must be 500 hours, not more than that!",
    }),
  level: z.enum(courseLevels, { message: "Level is required" }),
  category: z.enum(courseCategories, { message: "Category is required" }),
  smallDescription: z
    .string()
    .min(3, { message: "Is should be atleast 3 characters" })
    .max(100, { message: "It should be maximum of 100 characters" }),
  status: z.enum(courseStatus, { message: "Status should be added" }),
  slug: z
    .string()
    .min(3, { message: "Slug must be added of atleast 3 characters." })
    .max(100),
});

export type CourseSchemeType = z.infer<typeof courseSchema>;
