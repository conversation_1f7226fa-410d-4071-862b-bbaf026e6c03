import {
  Too<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>rov<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>rigger,
  TooltipContent,
  TooltipPortal,
} from "@radix-ui/react-tooltip";
import { Editor } from "@tiptap/react";
import { Toggle } from "../ui/toggle";
import { Bold } from "lucide-react";
import { cn } from "@/lib/utils"; // if you use cn

export function <PERSON>ubar({ editor }: { editor: Editor | null }) {
  if (!editor) return null;

  return (
    <div>
      <TooltipProvider>
        <div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Toggle
                size="sm"
                pressed={editor.isActive("bold")}
                onPressedChange={() =>
                  editor.chain().focus().toggleBold().run()
                }
                className={cn(
                  editor.isActive("bold") && "bg-muted text-muted-foreground"
                )}
              >
                <Bold
                  className={cn(
                    editor.isActive("bold") && "font-extrabold"
                  )}
                />
              </Toggle>
            </TooltipTrigger>
            <TooltipContent>Bold</TooltipContent>
          </Tooltip>
        </div>
      </TooltipProvider>
    </div>
  );
}