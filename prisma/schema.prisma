generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id
  name          String
  email         String
  emailVerified Boolean
  image         String?
  createdAt     DateTime
  updatedAt     DateTime
  sessions      Session[]
  accounts      Account[]
  courses       Course[] 

  @@unique([email])
  @@map("user")
}

model Session {
  id        String   @id
  expiresAt DateTime
  token     String
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@map("session")
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

model Course{
  id               String      @id @default(uuid())
  title            String
  description      String
  fileKey          String
  price            Int
  duration         Int
  level            CourseLevel @default(Beginner)
  category         String
  smallDescription String
  status           CourseStatus @default(Draft)
  slug             String      @unique
  createdAt        DateTime    @default(now())
  updatedAt        DateTime    @updatedAt
  userId           String?
  user             User?       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("course")
}

enum CourseStatus{
  Draft
  Published
  Archived
}

enum CourseLevel{
  Beginner
  Intermediate
  Advanced
}
